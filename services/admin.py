from django.contrib import admin
from .models import Service


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'isActive', 'tool_names_display', 'created_at', 'updated_at')
    list_filter = ('isActive', 'created_at', 'updated_at')
    search_fields = ('name', 'toolName')
    ordering = ('-created_at',)
    readonly_fields = ('id', 'created_at', 'updated_at', 'tool_names_display', 'tool_names_list')

    fieldsets = (
        (None, {
            'fields': ('name', 'isActive', 'toolName')
        }),
        ('Tool Names Info', {
            'fields': ('tool_names_display', 'tool_names_list'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """
        Customize queryset for admin
        """
        return super().get_queryset(request)
