from rest_framework import serializers
from .models import Service


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Service model with full CRUD operations
    """
    tool_names_display = serializers.ReadOnlyField()
    tool_names_list = serializers.ReadOnlyField()
    
    class Meta:
        model = Service
        fields = [
            'id', 'name', 'isActive', 'toolName', 
            'tool_names_display', 'tool_names_list',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'tool_names_display', 'tool_names_list']
    
    def validate_name(self, value):
        """
        Validate service name
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Service name cannot be empty")
        
        # Check for uniqueness, excluding current instance during updates
        queryset = Service.objects.filter(name__iexact=value.strip())
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError("A service with this name already exists")
        
        return value.strip()
    
    def validate_toolName(self, value):
        """
        Validate toolName field
        """
        if not value:
            raise serializers.ValidationError("Tool name(s) cannot be empty")
        
        # Validate based on type
        if isinstance(value, str):
            if not value.strip():
                raise serializers.ValidationError("Tool name cannot be empty")
            return value.strip()
        elif isinstance(value, list):
            if len(value) == 0:
                raise serializers.ValidationError("Tool names list cannot be empty")
            
            # Clean and validate each tool name
            cleaned_tools = []
            for tool in value:
                if not isinstance(tool, str):
                    raise serializers.ValidationError("All tool names must be strings")
                cleaned_tool = tool.strip()
                if not cleaned_tool:
                    raise serializers.ValidationError("Tool names cannot be empty")
                if cleaned_tool not in cleaned_tools:  # Remove duplicates
                    cleaned_tools.append(cleaned_tool)
            
            if len(cleaned_tools) == 0:
                raise serializers.ValidationError("At least one valid tool name is required")
            
            return cleaned_tools
        else:
            raise serializers.ValidationError("Tool names must be either a string or a list of strings")


class ServiceListSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for listing services
    """
    tool_names_display = serializers.ReadOnlyField()
    
    class Meta:
        model = Service
        fields = ['id', 'name', 'isActive', 'tool_names_display', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at', 'tool_names_display']


class ServiceCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating services
    """
    class Meta:
        model = Service
        fields = ['name', 'isActive', 'toolName']
    
    def validate_name(self, value):
        """
        Validate service name for creation
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Service name cannot be empty")
        
        if Service.objects.filter(name__iexact=value.strip()).exists():
            raise serializers.ValidationError("A service with this name already exists")
        
        return value.strip()
    
    def validate_toolName(self, value):
        """
        Validate toolName field for creation
        """
        if not value:
            raise serializers.ValidationError("Tool name(s) cannot be empty")
        
        # Validate based on type
        if isinstance(value, str):
            if not value.strip():
                raise serializers.ValidationError("Tool name cannot be empty")
            return value.strip()
        elif isinstance(value, list):
            if len(value) == 0:
                raise serializers.ValidationError("Tool names list cannot be empty")
            
            # Clean and validate each tool name
            cleaned_tools = []
            for tool in value:
                if not isinstance(tool, str):
                    raise serializers.ValidationError("All tool names must be strings")
                cleaned_tool = tool.strip()
                if not cleaned_tool:
                    raise serializers.ValidationError("Tool names cannot be empty")
                if cleaned_tool not in cleaned_tools:  # Remove duplicates
                    cleaned_tools.append(cleaned_tool)
            
            if len(cleaned_tools) == 0:
                raise serializers.ValidationError("At least one valid tool name is required")
            
            return cleaned_tools
        else:
            raise serializers.ValidationError("Tool names must be either a string or a list of strings")


class ServiceUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating services
    """
    class Meta:
        model = Service
        fields = ['name', 'isActive', 'toolName']
    
    def validate_name(self, value):
        """
        Validate service name for updates
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Service name cannot be empty")
        
        # Check for uniqueness, excluding current instance
        queryset = Service.objects.filter(name__iexact=value.strip())
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError("A service with this name already exists")
        
        return value.strip()
    
    def validate_toolName(self, value):
        """
        Validate toolName field for updates
        """
        if not value:
            raise serializers.ValidationError("Tool name(s) cannot be empty")
        
        # Validate based on type
        if isinstance(value, str):
            if not value.strip():
                raise serializers.ValidationError("Tool name cannot be empty")
            return value.strip()
        elif isinstance(value, list):
            if len(value) == 0:
                raise serializers.ValidationError("Tool names list cannot be empty")
            
            # Clean and validate each tool name
            cleaned_tools = []
            for tool in value:
                if not isinstance(tool, str):
                    raise serializers.ValidationError("All tool names must be strings")
                cleaned_tool = tool.strip()
                if not cleaned_tool:
                    raise serializers.ValidationError("Tool names cannot be empty")
                if cleaned_tool not in cleaned_tools:  # Remove duplicates
                    cleaned_tools.append(cleaned_tool)
            
            if len(cleaned_tools) == 0:
                raise serializers.ValidationError("At least one valid tool name is required")
            
            return cleaned_tools
        else:
            raise serializers.ValidationError("Tool names must be either a string or a list of strings")
