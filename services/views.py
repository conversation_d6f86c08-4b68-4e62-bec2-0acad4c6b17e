from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q

from .models import Service
from .serializers import (
    ServiceSerializer,
    ServiceListSerializer,
    ServiceCreateSerializer,
    ServiceUpdateSerializer
)
from authentication.permissions import IsAdminUser


class ServiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Service CRUD operations
    Only accessible by Admin users
    Provides full CRUD functionality with filtering and search
    """
    queryset = Service.objects.all()
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['isActive']
    search_fields = ['name', 'toolName']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == 'list':
            return ServiceListSerializer
        elif self.action == 'create':
            return ServiceCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return ServiceUpdateSerializer
        return ServiceSerializer

    def get_queryset(self):
        """
        Filter queryset based on query parameters
        """
        queryset = Service.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('isActive', None)
        if is_active is not None:
            queryset = queryset.filter(isActive=is_active.lower() == 'true')

        # Filter by tool name (supports both string and list searches)
        tool_name = self.request.query_params.get('toolName', None)
        if tool_name:
            # Search in both string and JSON array formats
            queryset = queryset.filter(
                Q(toolName__icontains=tool_name) |  # For string toolNames
                Q(toolName__contains=tool_name)     # For JSON array toolNames
            )

        return queryset

    def create(self, request, *args, **kwargs):
        """
        Create a new service
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        service = serializer.save()

        # Return full service data
        response_serializer = ServiceSerializer(service)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """
        Update a service
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        service = serializer.save()

        # Return full service data
        response_serializer = ServiceSerializer(service)
        return Response(response_serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        Soft delete a service by setting isActive to False
        """
        instance = self.get_object()
        instance.isActive = False
        instance.save()

        # Return updated service data
        serializer = ServiceSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=['patch'], url_path='activate')
    def activate(self, request, pk=None):
        """
        Activate a service (set isActive to True)
        """
        service = self.get_object()
        service.isActive = True
        service.save()

        serializer = ServiceSerializer(service)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'], url_path='deactivate')
    def deactivate(self, request, pk=None):
        """
        Deactivate a service (set isActive to False)
        """
        service = self.get_object()
        service.isActive = False
        service.save()

        serializer = ServiceSerializer(service)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='active')
    def active_services(self, request):
        """
        Get only active services
        """
        queryset = self.get_queryset().filter(isActive=True)

        # Apply search and ordering
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='inactive')
    def inactive_services(self, request):
        """
        Get only inactive services
        """
        queryset = self.get_queryset().filter(isActive=False)

        # Apply search and ordering
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='search-by-tool')
    def search_by_tool(self, request):
        """
        Search services by tool name
        """
        tool_name = request.query_params.get('tool', None)
        if not tool_name:
            return Response(
                {'error': 'Tool name parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Search in both string and JSON array formats
        queryset = self.get_queryset().filter(
            Q(toolName__icontains=tool_name) |  # For string toolNames
            Q(toolName__contains=tool_name)     # For JSON array toolNames
        )

        # Apply other filters
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(queryset, many=True)
        return Response(serializer.data)
