from django.db import models
from django.core.exceptions import ValidationError
import json


def validate_tool_names(value):
    """
    Validate that toolName is either a string or a list of strings
    """
    if isinstance(value, str):
        return
    elif isinstance(value, list):
        if not all(isinstance(item, str) for item in value):
            raise ValidationError("All tool names must be strings")
        if len(value) == 0:
            raise ValidationError("Tool names list cannot be empty")
    else:
        raise ValidationError("Tool names must be either a string or a list of strings")


class Service(models.Model):
    """
    Service model for managing services with tool names
    """
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255, unique=True, help_text="Service name")
    isActive = models.BooleanField(default=True, help_text="Whether the service is active")
    toolName = models.JSONField(
        validators=[validate_tool_names],
        help_text="Tool name(s) - can be a single string or list of strings"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'services_service'
        ordering = ['-created_at']
        verbose_name = 'Service'
        verbose_name_plural = 'Services'

    def __str__(self):
        return self.name

    def clean(self):
        """
        Additional validation for the model
        """
        super().clean()
        if self.toolName:
            validate_tool_names(self.toolName)

    def save(self, *args, **kwargs):
        """
        Override save to run full_clean validation
        """
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def tool_names_list(self):
        """
        Return tool names as a list regardless of storage format
        """
        if isinstance(self.toolName, str):
            return [self.toolName]
        return self.toolName or []

    @property
    def tool_names_display(self):
        """
        Return a formatted string of tool names for display
        """
        tool_names = self.tool_names_list
        if len(tool_names) == 1:
            return tool_names[0]
        return ", ".join(tool_names)
